/**
 * ServerPanel component for server switching and management
 */

import React from 'react';
import { Server, Plus, Wifi, WifiOff, Loader } from 'lucide-react';
import { motion } from 'framer-motion';
import { ServerPanelProps } from '@/types';

const ServerPanel: React.FC<ServerPanelProps> = ({
  servers,
  currentServerId,
  onServerSelect,
  onAddServer
}) => {
  const getStatusIcon = (status: 'connected' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return <Wifi size={14} className="text-theme-secondary" />;
      case 'disconnected':
        return <WifiOff size={14} className="text-theme-tertiary" />;
      default:
        return <WifiOff size={14} className="text-theme-tertiary" />;
    }
  };

  const getStatusColor = (status: 'connected' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return 'border-theme-primary bg-surface-primary';
      case 'disconnected':
        return 'border-theme-primary bg-surface-secondary';
      default:
        return 'border-theme-primary bg-surface-primary';
    }
  };

  return (
    <div className="space-y-3">
      {/* Panel Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
          <Server size={16} className="text-accent-primary" />
          Servers
        </h3>
      </div>

      {/* Server List */}
      <div className="space-y-2">
        {servers.map((server) => (
          <motion.button
            key={server.id}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
            onClick={() => onServerSelect(server.id)}
            className={`
              w-full p-3 rounded-lg border transition-all duration-200 text-left relative
              ${currentServerId === server.id
                ? 'border-accent-primary bg-accent-primary/10'
                : `${getStatusColor(server.status)} hover:bg-surface-secondary hover:border-accent-primary/50`
              }
            `}
          >
            {/* Active Server Indicator */}
            {currentServerId === server.id && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent-primary rounded-full border-2 border-theme-primary"></div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 min-w-0 flex-1">
                {/* Server Icon */}
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-theme-secondary flex items-center justify-center">
                  <Server size={16} className="text-theme-primary" />
                </div>
                
                {/* Server Info */}
                <div className="min-w-0 flex-1">
                  <div className="font-medium text-theme-primary text-sm truncate">
                    {server.name}
                  </div>
                  <div className="text-xs text-theme-tertiary truncate">
                    {server.host}
                  </div>
                </div>
              </div>

              {/* Status Icon */}
              <div className="flex-shrink-0">
                {getStatusIcon(server.status)}
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Add Server Button */}
      <motion.button
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
        onClick={onAddServer}
        className="w-full p-3 border border-dashed border-theme-primary hover:border-accent-primary rounded-lg transition-all duration-200 group hover:bg-surface-secondary"
      >
        <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
          <Plus size={16} />
          <span className="text-sm font-medium">Add Server</span>
        </div>
      </motion.button>
    </div>
  );
};

export default ServerPanel;
